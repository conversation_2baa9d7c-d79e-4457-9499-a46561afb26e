import{d as A,r as v,i as C,o as T,c as n,a as s,l as V,w as b,v as w,t as r,m as S,n as F,F as L,j as B,h as l,_ as q}from"./index-CrANV0st.js";import{m as M}from"./bookmarkService-wwcTVc0m.js";const N={class:"messages-view"},U={class:"container"},$={class:"message-form-section"},z={class:"form-card"},E={class:"form-group"},j={class:"form-group"},I={class:"char-count"},G=["disabled"],H={key:0},J={key:1},K={class:"messages-section"},O={class:"section-header"},P=["disabled"],Q={key:0,class:"loading-state"},R={key:1,class:"error-state"},W={key:2,class:"messages-list"},X={class:"message-header"},Y={class:"message-author"},Z={class:"author-avatar"},ss={class:"author-info"},es={class:"author-name"},ts={class:"message-time"},as={class:"message-content"},os={key:3,class:"empty-state"},ns=A({__name:"MessagesView",setup(ls){const c=v([]),d=v(!0),i=v(null),u=v(!1),o=v({name:"",content:""}),f=C(()=>o.value.name.trim().length>0&&o.value.content.trim().length>0),g=async()=>{try{d.value=!0,i.value=null;const t=await M.getAllMessages();if(t.success&&t.data){const e=t.data.messages||[];c.value=e.sort((a,m)=>new Date(m.createdAt).getTime()-new Date(a.createdAt).getTime())}else i.value=t.error||"加载留言失败",c.value=[]}catch(t){console.error("Failed to load messages:",t),i.value="网络连接失败",c.value=[]}finally{d.value=!1}},k=async()=>{if(!(!f.value||u.value))try{u.value=!0;const t=await M.addMessage({name:o.value.name.trim(),content:o.value.content.trim()});t.success&&t.data?(await g(),o.value={name:"",content:""},x()):i.value=t.error||"提交留言失败"}catch(t){console.error("Failed to submit message:",t),i.value="网络连接失败"}finally{u.value=!1}},x=()=>{const t=document.querySelector(".success-message");t&&(t.classList.add("show"),setTimeout(()=>{t.classList.remove("show")},3e3))},D=t=>{const e=new Date(t),m=new Date().getTime()-e.getTime(),p=Math.floor(m/(1e3*60)),_=Math.floor(m/(1e3*60*60)),y=Math.floor(m/(1e3*60*60*24));return p<1?"刚刚":p<60?`${p}分钟前`:_<24?`${_}小时前`:y<30?`${y}天前`:e.toLocaleDateString("zh-CN")},h=()=>{g()};return T(()=>{g()}),(t,e)=>(l(),n("div",N,[s("div",U,[e[12]||(e[12]=s("div",{class:"messages-header"},[s("h1",{class:"page-title"},"留言板"),s("p",{class:"page-subtitle"},"分享你的想法，留下美好的回忆")],-1)),s("div",$,[s("div",z,[e[4]||(e[4]=s("h2",{class:"form-title"},"写下你的留言",-1)),s("form",{onSubmit:V(k,["prevent"]),class:"message-form"},[s("div",E,[e[2]||(e[2]=s("label",{for:"name",class:"form-label"},"姓名",-1)),b(s("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=a=>o.value.name=a),type:"text",placeholder:"请输入你的姓名",class:"form-input",maxlength:"20",required:""},null,512),[[w,o.value.name]])]),s("div",j,[e[3]||(e[3]=s("label",{for:"content",class:"form-label"},"留言内容",-1)),b(s("textarea",{id:"content","onUpdate:modelValue":e[1]||(e[1]=a=>o.value.content=a),placeholder:"分享你的想法...",class:"form-textarea",rows:"4",maxlength:"500",required:""},null,512),[[w,o.value.content]]),s("div",I,r(o.value.content.length)+"/500",1)]),s("button",{type:"submit",disabled:!f.value||u.value,class:"submit-btn"},[u.value?(l(),n("span",H,"提交中...")):(l(),n("span",J,"发送留言"))],8,G)],32)])]),e[13]||(e[13]=s("div",{class:"success-message"},[s("div",{class:"success-content"},[s("span",{class:"success-icon"},"✅"),s("span",null,"留言提交成功！")])],-1)),s("div",K,[s("div",O,[e[7]||(e[7]=s("h2",{class:"section-title"},"所有留言",-1)),s("button",{onClick:h,class:"refresh-btn",disabled:d.value},[(l(),n("svg",{class:F(["refresh-icon",{spinning:d.value}]),width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},e[5]||(e[5]=[s("polyline",{points:"23 4 23 10 17 10"},null,-1),s("polyline",{points:"1 20 1 14 7 14"},null,-1),s("path",{d:"m20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"},null,-1)]),2)),e[6]||(e[6]=S(" 刷新 "))],8,P)]),d.value?(l(),n("div",Q,e[8]||(e[8]=[s("div",{class:"loading-spinner"},null,-1),s("p",null,"正在加载留言...",-1)]))):i.value?(l(),n("div",R,[e[9]||(e[9]=s("div",{class:"error-icon"},"⚠️",-1)),e[10]||(e[10]=s("h3",null,"加载失败",-1)),s("p",null,r(i.value),1),s("button",{onClick:h,class:"retry-btn"},"重试")])):c.value.length>0?(l(),n("div",W,[(l(!0),n(L,null,B(c.value,a=>(l(),n("div",{key:a.id,class:"message-card"},[s("div",X,[s("div",Y,[s("div",Z,r(a.name.charAt(0).toUpperCase()),1),s("div",ss,[s("h4",es,r(a.name),1),s("span",ts,r(D(a.createdAt)),1)])])]),s("div",as,[s("p",null,r(a.content),1)])]))),128))])):(l(),n("div",os,e[11]||(e[11]=[s("div",{class:"empty-icon"},"💭",-1),s("h3",null,"还没有留言",-1),s("p",null,"成为第一个留言的人吧！",-1)])))])])]))}}),cs=q(ns,[["__scopeId","data-v-9a4f0e3b"]]);export{cs as default};
