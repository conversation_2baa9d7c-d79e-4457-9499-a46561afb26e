import{d as N,r as c,o as W,i as E,c as n,a as e,f as h,l as w,w as p,v as b,t as u,m as A,F as C,j as V,p as L,k as j,h as i,_ as z}from"./index-CrANV0st.js";import{b as v}from"./bookmarkService-wwcTVc0m.js";const G={class:"admin-view"},H={class:"container"},I={key:0,class:"login-section"},P={class:"login-card"},R={class:"form-group"},J=["disabled"],K=["disabled"],O={key:0},Q={key:1},X={key:0,class:"error-message"},Y={key:1,class:"admin-section"},Z={key:0,class:"error-banner"},ee={key:1,class:"loading-state"},se={key:2,class:"websites-list"},le={class:"website-info"},te={class:"website-icon"},oe={class:"website-details"},ae={class:"website-name"},ne={class:"website-description"},ie={class:"website-meta"},re={class:"website-category"},ue={class:"website-url"},de={class:"website-actions"},ce=["onClick"],ve=["onClick"],pe={key:3,class:"empty-state"},me={class:"modal-header"},fe={class:"form-row"},be={class:"form-group"},_e={class:"form-group"},ye={class:"form-group"},ke={class:"form-group"},ge={class:"form-row"},he={class:"form-group"},we=["value"],Ce={class:"form-group"},Ve={class:"color-picker"},xe={class:"color-presets"},Ae=["onClick"],Ue={class:"form-actions"},Be=["disabled"],Fe={key:0},Se={key:1},Te=N({__name:"AdminView",setup($e){const y=c(!1),d=c(""),k=c([]),r=c(!1),a=c(null),_=c(!1),m=c(null),t=c({name:"",description:"",url:"",icon:"🔗",category:"",color:"#00d4ff"}),U=["开发工具","系统工具","效率工具","设计工具","学习资源","娱乐","其他"],B=["#00d4ff","#ff3366","#8b5cf6","#00ff88","#ffa500","#ff69b4","#32cd32"],F=async()=>{if(!d.value.trim()){a.value="请输入管理员密码";return}r.value=!0,a.value=null;try{await v.verifyAdminPassword(d.value)?(v.setAdminToken(d.value),y.value=!0,await g()):a.value="密码错误"}catch{a.value="验证失败，请检查网络连接"}finally{r.value=!1}},g=async()=>{r.value=!0;try{const o=await v.getAllBookmarks();o.success&&o.data?k.value=o.data:a.value=o.error||"加载数据失败"}catch{a.value="网络连接失败"}finally{r.value=!1}},f=()=>{t.value={name:"",description:"",url:"",icon:"🔗",category:"",color:"#00d4ff"},m.value=null,_.value=!1},S=()=>{f(),_.value=!0},T=o=>{t.value={...o},m.value=o,_.value=!0},$=async()=>{if(!t.value.name.trim()||!t.value.url.trim()||!t.value.description.trim()){a.value="请填写所有必填字段";return}try{new URL(t.value.url)}catch{a.value="请输入有效的网址";return}r.value=!0,a.value=null;try{let o;m.value?o=await v.updateBookmark(m.value.id,t.value):o=await v.addBookmark(t.value),o.success?(await g(),f()):a.value=o.error||"操作失败"}catch{a.value="网络连接失败"}finally{r.value=!1}},M=async o=>{if(confirm(`确定要删除 "${o.name}" 吗？`)){r.value=!0,a.value=null;try{const s=await v.deleteBookmark(o.id);s.success?await g():a.value=s.error||"删除失败"}catch{a.value="网络连接失败"}finally{r.value=!1}}},q=()=>{v.clearAdminToken(),y.value=!1,d.value="",k.value=[],f()};W(()=>{const o=v.getAdminToken();o&&(d.value=o,y.value=!0,g())});const D=E(()=>t.value.name.trim()&&t.value.url.trim()&&t.value.description.trim());return(o,s)=>(i(),n("div",G,[e("div",H,[y.value?(i(),n("div",Y,[e("div",{class:"admin-header"},[s[12]||(s[12]=e("div",{class:"header-left"},[e("h1",{class:"page-title"},"书签管理"),e("p",{class:"page-subtitle"},"管理你的网站收藏")],-1)),e("div",{class:"header-right"},[e("button",{onClick:S,class:"add-btn"},s[11]||(s[11]=[e("span",{class:"btn-icon"},"+",-1),A(" 添加书签 ")])),e("button",{onClick:q,class:"logout-btn"},"退出")])]),a.value?(i(),n("div",Z,[s[13]||(s[13]=e("span",{class:"error-icon"},"⚠️",-1)),A(" "+u(a.value)+" ",1),e("button",{onClick:s[1]||(s[1]=l=>a.value=null),class:"close-error"},"×")])):h("",!0),r.value&&!_.value?(i(),n("div",ee,s[14]||(s[14]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"加载中...",-1)]))):(i(),n("div",se,[(i(!0),n(C,null,V(k.value,l=>(i(),n("div",{key:l.id,class:"website-item"},[e("div",le,[e("div",te,u(l.icon),1),e("div",oe,[e("h3",ae,u(l.name),1),e("p",ne,u(l.description),1),e("div",ie,[e("span",re,u(l.category),1),e("span",ue,u(l.url),1)])])]),e("div",de,[e("button",{onClick:x=>T(l),class:"edit-btn"},"编辑",8,ce),e("button",{onClick:x=>M(l),class:"delete-btn"},"删除",8,ve)])]))),128))])),!r.value&&k.value.length===0?(i(),n("div",pe,s[15]||(s[15]=[e("div",{class:"empty-icon"},"📚",-1),e("h3",null,"还没有书签",-1),e("p",null,'点击"添加书签"按钮开始添加你的第一个书签',-1)]))):h("",!0)])):(i(),n("div",I,[e("div",P,[s[9]||(s[9]=e("h1",{class:"login-title"},"管理员登录",-1)),s[10]||(s[10]=e("p",{class:"login-subtitle"},"请输入管理员密码以管理书签",-1)),e("form",{onSubmit:w(F,["prevent"]),class:"login-form"},[e("div",R,[p(e("input",{"onUpdate:modelValue":s[0]||(s[0]=l=>d.value=l),type:"password",placeholder:"管理员密码",class:"form-input",disabled:r.value},null,8,J),[[b,d.value]])]),e("button",{type:"submit",class:"login-btn",disabled:r.value||!d.value.trim()},[r.value?(i(),n("span",O,"验证中...")):(i(),n("span",Q,"登录"))],8,K)],32),a.value?(i(),n("div",X,u(a.value),1)):h("",!0)])])),_.value?(i(),n("div",{key:2,class:"modal-overlay",onClick:f},[e("div",{class:"modal-content",onClick:s[8]||(s[8]=w(()=>{},["stop"]))},[e("div",me,[e("h2",null,u(m.value?"编辑书签":"添加书签"),1),e("button",{onClick:f,class:"close-btn"},"×")]),e("form",{onSubmit:w($,["prevent"]),class:"bookmark-form"},[e("div",fe,[e("div",be,[s[16]||(s[16]=e("label",null,"网站名称 *",-1)),p(e("input",{"onUpdate:modelValue":s[2]||(s[2]=l=>t.value.name=l),type:"text",placeholder:"例如：GitHub",class:"form-input",required:""},null,512),[[b,t.value.name]])]),e("div",_e,[s[17]||(s[17]=e("label",null,"图标",-1)),p(e("input",{"onUpdate:modelValue":s[3]||(s[3]=l=>t.value.icon=l),type:"text",placeholder:"🔗",class:"form-input icon-input"},null,512),[[b,t.value.icon]])])]),e("div",ye,[s[18]||(s[18]=e("label",null,"网站描述 *",-1)),p(e("textarea",{"onUpdate:modelValue":s[4]||(s[4]=l=>t.value.description=l),placeholder:"简短描述这个网站的用途...",class:"form-textarea",required:""},null,512),[[b,t.value.description]])]),e("div",ke,[s[19]||(s[19]=e("label",null,"网站地址 *",-1)),p(e("input",{"onUpdate:modelValue":s[5]||(s[5]=l=>t.value.url=l),type:"url",placeholder:"https://example.com",class:"form-input",required:""},null,512),[[b,t.value.url]])]),e("div",ge,[e("div",he,[s[21]||(s[21]=e("label",null,"分类",-1)),p(e("select",{"onUpdate:modelValue":s[6]||(s[6]=l=>t.value.category=l),class:"form-select"},[s[20]||(s[20]=e("option",{value:""},"选择分类",-1)),(i(),n(C,null,V(U,l=>e("option",{key:l,value:l},u(l),9,we)),64))],512),[[L,t.value.category]])]),e("div",Ce,[s[22]||(s[22]=e("label",null,"主题色",-1)),e("div",Ve,[p(e("input",{"onUpdate:modelValue":s[7]||(s[7]=l=>t.value.color=l),type:"color",class:"color-input"},null,512),[[b,t.value.color]]),e("div",xe,[(i(),n(C,null,V(B,l=>e("button",{key:l,type:"button",class:"color-preset",style:j({backgroundColor:l}),onClick:x=>t.value.color=l},null,12,Ae)),64))])])])]),e("div",Ue,[e("button",{type:"button",onClick:f,class:"cancel-btn"},"取消"),e("button",{type:"submit",class:"submit-btn",disabled:r.value||!D.value},[r.value?(i(),n("span",Fe,"保存中...")):(i(),n("span",Se,u(m.value?"更新":"添加"),1))],8,Be)])],32)])])):h("",!0)])]))}}),De=z(Te,[["__scopeId","data-v-86c748f6"]]);export{De as default};
